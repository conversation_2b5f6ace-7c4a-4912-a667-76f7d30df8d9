import logging
from typing import Literal

from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    ENVIRONMENT: Literal["development", "testing", "production"] = Field(
        default="development", description="运行环境"
    )
    DEBUG: bool = Field(default=True, description="调试模式")
    VERSION: str = Field(default="1.0.0", description="应用版本")

    HOST: str = Field(default="0.0.0.0", description="服务主机")
    PORT: int = Field(default=30101, ge=1, le=65535, description="服务端口")

    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FILE_MAX_SIZE: int = Field(
        default=10 * 1024 * 1024, description="日志文件最大大小(字节)"
    )
    LOG_FILE_BACKUP_COUNT: int = Field(default=5, description="日志文件备份数量")

    DIFY_API_KEY: str = Field(default="", description="Dify API 密钥")
    DIFY_API_URL: str = Field(default="", description="Dify API 地址")
    DIFY_USER: str = Field(default="admin", description="Dify 用户")

    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL必须是以下值之一: {', '.join(valid_levels)}")
        return v.upper()

    @model_validator(mode="after")
    def validate_environment_settings(self):
        """验证环境相关配置"""
        if self.ENVIRONMENT == "production" and self.DEBUG:
            logging.warning("生产环境不建议开启调试模式")

        if self.ENVIRONMENT != "development":
            if not self.DIFY_API_KEY:
                raise ValueError("非开发环境必须配置DIFY_API_KEY")
            if not self.DIFY_API_URL:
                raise ValueError("非开发环境必须配置DIFY_API_URL")

        return self

    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == "development"

    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == "production"

    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.ENVIRONMENT == "testing"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        env_prefix = ""
        extra = "ignore"


def get_settings() -> Settings:
    """获取配置实例"""
    try:
        return Settings()
    except Exception as e:
        raise ValueError(f"配置加载失败: {str(e)}")


# 全局配置实例
settings = get_settings()
