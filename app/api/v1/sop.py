from typing import Any, Dict

from fastapi import APIRouter, Depends, File, Form, UploadFile

from app.dependencies.common import get_sop_service
from app.services.sop_service import SOPService

router = APIRouter()


@router.post("/upload", summary="上传SOP文件")
async def upload_sop_file(
    file: UploadFile = File(...),
    max_len: int = Form(40),
    sop_service: SOPService = Depends(get_sop_service),
) -> Dict[str, Any]:
    return await sop_service.process_upload(file, max_len)
